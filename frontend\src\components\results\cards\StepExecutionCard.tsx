import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import {
  Clock,
  Code,
  Play,
  CheckCircle,
  XCircle,
  RotateCcw,
  Lightbulb,
  Eye,
  EyeOff,
  AlertTriangle
} from 'lucide-react';
import { ResultCard } from '@/types/analysis';
import apiService from '@/lib/api';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface StepExecutionCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

interface StepExecutionContent {
  status: 'started' | 'generating_code' | 'code_generated' | 'code_executed' | 'generating_insights' | 'completed' | 'failed' | 'retrying';
  step_name: string;
  attempt?: number;
  message: string;
  code?: string;
  execution_result?: {
    success: boolean;
    stdout?: string;
    stderr?: string;
    warnings?: string;
    plots?: string[];
    execution_time?: number;
  };
  error?: string;
  insights?: string[];
}

export function StepExecutionCard({ card, isHighlighted }: StepExecutionCardProps) {
  const [showDetails, setShowDetails] = useState(true);
  const content = card.content as StepExecutionContent;

  // 当卡片有实际内容时，确保详细信息展开
  useEffect(() => {
    const hasContent = content.code || content.execution_result || content.insights;
    if (hasContent && !showDetails) {
      console.log('[DEBUG] StepExecutionCard 检测到内容，自动展开详细信息');
      setShowDetails(true);
    }
  }, [content.code, content.execution_result, content.insights, showDetails]);

  // 添加调试日志
  console.log('[DEBUG] StepExecutionCard 渲染:', {
    cardId: card.id,
    nodeId: card.node_id,
    isHighlighted,
    status: content.status,
    hasCode: !!content.code,
    hasExecutionResult: !!content.execution_result,
    hasInsights: !!content.insights,
    showDetails,
    timestamp: card.timestamp
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'started':
        return (
          <div className="relative">
            <Play className="w-4 h-4 text-primary animate-pulse" />
            <div className="absolute inset-0 w-4 h-4 rounded-full bg-primary/20 animate-ping" />
          </div>
        );
      case 'generating_code':
        return (
          <div className="relative">
            <Code className="w-4 h-4 text-warning animate-bounce" />
            <div className="absolute -inset-1 w-6 h-6 rounded-full bg-warning/10 animate-pulse" />
          </div>
        );
      case 'code_generated':
        return <CheckCircle className="w-4 h-4 text-success" />;
      case 'code_executed':
        return content.execution_result?.success ?
          <CheckCircle className="w-4 h-4 text-success" /> :
          <XCircle className="w-4 h-4 text-destructive" />;
      case 'generating_insights':
        return (
          <div className="relative">
            <Lightbulb className="w-4 h-4 text-accent animate-pulse" />
            <div className="absolute -inset-0.5 w-5 h-5 rounded-full bg-accent/20 animate-ping" />
          </div>
        );
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-success" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-destructive" />;
      case 'retrying':
        return (
          <div className="relative">
            <RotateCcw className="w-4 h-4 text-warning animate-spin" />
            <div className="absolute -inset-1 w-6 h-6 rounded-full border-2 border-warning/30 border-t-warning animate-spin" />
          </div>
        );
      default:
        return <Clock className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'retrying':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'generating_code':
      case 'generating_insights':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'code_generated':
      case 'code_executed':
        return content.execution_result?.success ? 
          'bg-green-100 text-green-800 border-green-200' : 
          'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getProgressValue = (status: string) => {
    switch (status) {
      case 'started': return 10;
      case 'generating_code': return 25;
      case 'code_generated': return 40;
      case 'code_executed': return content.execution_result?.success ? 70 : 30;
      case 'generating_insights': return 85;
      case 'completed': return 100;
      case 'failed': return 0;
      case 'retrying': return 20;
      default: return 0;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'started': return '开始执行';
      case 'generating_code': return '正在生成代码';
      case 'code_generated': return '代码生成完成';
      case 'code_executed': return content.execution_result?.success ? '代码执行成功' : '代码执行失败';
      case 'generating_insights': return '正在生成洞察';
      case 'completed': return '步骤完成';
      case 'failed': return '执行失败';
      case 'retrying': return '准备重试';
      default: return status;
    }
  };

  const isRunning = ['started', 'generating_code', 'generating_insights', 'retrying'].includes(content.status);

  return (
    <Card className={cn(
      "result-card animate-fade-in-up transition-all duration-300 hover:shadow-lg",
      isHighlighted && "ring-2 ring-primary shadow-lg shadow-primary/20",
      content.status === 'failed' && "border-destructive/50 bg-destructive/5",
      ['generating_code', 'generating_insights'].includes(content.status) && "border-l-4 border-l-warning bg-warning/5",
      isRunning && "border-l-4 border-l-primary bg-primary/5 animate-processing-glow"
    )}>
      <CardHeader className="pb-3 relative">
        {/* Running indicator */}
        {isRunning && (
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-primary animate-pulse" />
        )}
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-3">
            {getStatusIcon(content.status)}
            <span className="font-semibold text-foreground">{content.step_name}</span>
            {isRunning && (
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            {content.attempt && (
              <Badge variant="outline" className="text-xs font-medium">
                尝试 {content.attempt}
              </Badge>
            )}
            <Badge className={cn("text-xs font-medium shadow-sm", getStatusColor(content.status))}>
              {getStatusText(content.status)}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Enhanced Progress Bar */}
        <div className="space-y-3">
          <div className="flex justify-between text-xs font-medium">
            <span className="text-muted-foreground">执行进度</span>
            <span className={cn(
              "font-mono",
              isRunning ? "text-primary" : "text-muted-foreground"
            )}>
              {getProgressValue(content.status)}%
            </span>
          </div>
          <div className="relative">
            <Progress
              value={getProgressValue(content.status)}
              className={cn(
                "h-2.5 transition-all duration-500",
                isRunning && "animate-pulse"
              )}
            />
            {isRunning && (
              <div className="absolute inset-0 h-2.5 rounded-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
            )}
          </div>
        </div>

        {/* Enhanced Status Message */}
        <div className={cn(
          "text-sm p-3 rounded-lg border transition-all duration-200",
          isRunning ? "bg-primary/5 border-primary/20 text-primary" : "bg-muted/30 border-border text-muted-foreground"
        )}>
          <div className="flex items-center gap-2">
            {isRunning && <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />}
            {content.message}
          </div>
        </div>

        {/* 详细内容 */}
        <Collapsible open={showDetails} onOpenChange={setShowDetails}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="w-full justify-between">
              <span className="text-sm font-medium">详细信息</span>
              {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-4">
            {/* 生成的代码 */}
            {content.code && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <Code className="w-4 h-4" />
                  生成的代码
                </h4>
                <div className="border rounded-lg overflow-hidden" style={{ minHeight: '120px', maxHeight: '600px' }}>
                  <SyntaxHighlighter
                    language="python"
                    style={tomorrow}
                    customStyle={{
                      margin: 0,
                      padding: '12px',
                      fontSize: '13px',
                      lineHeight: '1.4',
                      minHeight: '120px',
                      maxHeight: '600px',
                      overflow: 'auto',
                    }}
                    showLineNumbers={true}
                    wrapLines={true}
                    wrapLongLines={true}
                  >
                    {content.code}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {/* 执行结果 */}
            {content.execution_result && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <Play className="w-4 h-4" />
                  执行结果
                </h4>
                
                {/* 控制台输出 */}
                {content.execution_result.stdout && (
                  <div className="border rounded-lg bg-green-50 p-3" style={{ maxHeight: '500px', overflow: 'auto' }}>
                    <pre className="text-sm text-green-700 whitespace-pre-wrap break-words" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                      {content.execution_result.stdout}
                    </pre>
                  </div>
                )}

                {/* 警告信息 */}
                {content.execution_result.warnings && (
                  <div className="border rounded-lg bg-yellow-50 border-yellow-200 p-3" style={{ maxHeight: '200px', overflow: 'auto' }}>
                    <div className="text-sm text-yellow-800 flex items-start gap-2">
                      <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0 text-yellow-600" />
                      <div className="flex-1">
                        <strong>警告:</strong>
                        <pre className="mt-1 whitespace-pre-wrap break-words" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                          {content.execution_result.warnings}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}

                {/* 错误输出 */}
                {content.execution_result.stderr && (
                  <div className="border rounded-lg bg-red-50 border-red-200 p-3" style={{ maxHeight: '200px', overflow: 'auto' }}>
                    <div className="text-sm text-red-800 flex items-start gap-2">
                      <XCircle className="w-4 h-4 mt-0.5 flex-shrink-0 text-red-600" />
                      <div className="flex-1">
                        <strong>错误:</strong>
                        <pre className="mt-1 whitespace-pre-wrap break-words" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                          {content.execution_result.stderr}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}

                {/* 图片结果 */}
                {content.execution_result.plots && content.execution_result.plots.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="text-xs font-medium text-muted-foreground">生成的图表</h5>
                    <div className="grid gap-3">
                      {content.execution_result.plots.map((plotPath: string, index: number) => (
                        <div key={index} className="border rounded-lg overflow-hidden bg-white">
                          <img
                            src={apiService.getStaticFileUrl(plotPath)}
                            alt={`图表 ${index + 1}`}
                            className="w-full h-auto object-contain"
                            style={{
                              maxWidth: '100%',
                              maxHeight: '400px',
                              minHeight: '200px'
                            }}
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 执行时间 */}
                {content.execution_result.execution_time && (
                  <div className="text-xs text-muted-foreground">
                    执行时间: {content.execution_result.execution_time.toFixed(2)}s
                  </div>
                )}
              </div>
            )}

            {/* 错误信息 */}
            {content.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md" style={{ maxWidth: '100%' }}>
                <div className="text-sm text-red-800 flex items-start gap-2">
                  <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <strong>错误:</strong>
                    <div className="mt-1 whitespace-pre-wrap break-words" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                      {content.error}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 洞察结果 */}
            {content.insights && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <Lightbulb className="w-4 h-4" />
                  分析洞察
                </h4>
                <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                  <div className="text-sm text-purple-800 whitespace-pre-wrap">
                    {typeof content.insights === 'string'
                      ? content.insights
                      : Array.isArray(content.insights)
                        ? content.insights.join('\n\n')
                        : JSON.stringify(content.insights)
                    }
                  </div>
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* 时间戳 */}
        <div className="text-xs text-muted-foreground text-right">
          {card.timestamp}
        </div>
      </CardContent>
    </Card>
  );
}
